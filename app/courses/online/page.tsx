import Image from 'next/image'
import Link from 'next/link'
import { title } from 'process'

export default function CoursesPage() {
  const courses = [
    {
      title: "Acting for Film (Online)",
      slug: 'acting-for-film-online',
      duration: "4 weeks",
      description: "Online hands-on training workshop that trains you on how to act for Film and Television.",
      image: "/images/film-and-media-course/acting-for-film-min.jpeg",
      type: 'online'
    },
    {
      title: "Post Production",
      duration: "4 weeks",
      description: "The Final Cut: Mastering the Art of Post Production for Film and TV",
      image: "/images/online-courses/dca-website-banner-post-prod.jpg",
      type: 'online'
    },
    {
      title: "Fashion, Art & Crafts",
      duration: "4 weeks",
      description: "Unleashing Your Creativity: Exploring the World of Fashion, Art & Crafts",
      image: "/images/online-courses/dca-website-banner-fashion.jpg",
      type: 'online'
    },
    {
      title: "Digital Film Making",
      duration: "4 weeks",
      description: "Bringing Your Vision to Life: The Fundamentals of Digital Film making",
      image: "/images/online-courses/dca-website-banner-digital-film.jpg",
      type: 'online'
    },
    {
      title: "Documentary Film-making",
      duration: "4 weeks",
      description: "Telling True Stories: The Art of Documentary Film-making",
      image: "/images/online-courses/dca-website-banner-documentary.jpg",
      type: 'online'
    },
    {
      title: "Digital Marketing",
      duration: "4 weeks",
      description: "Transform Your Online Presence: Mastering the Fundamentals of Digital Marketing",
      image: "/images/online-courses/dca-website-banner-digital-marketing.jpg",
      type: 'online'
    },
    {
      title: "Producing And The Business Of Film & TV",
      duration: "4 weeks",
      description: "From Script to Screen: Mastering the Art of Film and TV Production",
      image: "/images/online-courses/prod-business-of-film.jpg",
      type: 'online'
    },
  ]

  return (
    <div className="min-h-screen bg-black text-white py-24">
      <div className="max-w-6xl mx-auto px-4">
        <h1 className="text-5xl font-bold mb-8">Our Courses</h1>
        <h2 className="text-3xl font-bold mb-8">Online <span className='text-brand'>Courses</span></h2>
        <p className="text-xl text-gray-300 mb-12">
Our comprehensive training programs equip you with the skills and expertise needed to succeed in the creative industry. Gain practical experience, build a portfolio, and stay ahead of the curve with industry-leading curriculum. Launch your career with confidence and achieve your goals in graphic design, digital media, photography, and more.
        </p>

        <div className="grid md:grid-cols-3 gap-8">
          {courses.filter(course => course.type === 'online').map((course, index) => (
            <div key={index} className="bg-gray-900 rounded-lg overflow-hidden">
              <Image
                src={course.image || "/placeholder.svg"}
                alt={course.title}
                width={400}
                height={300}
                className="w-full h-64  object-cover"
              />
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-lg font-bold">{course.title}</h3>
                  <span className="text-sm bg-gray-800 px-3 py-1 rounded whitespace-nowrap">{course.duration}</span>
                </div>
                <p className="text-gray-300 mb-4">{course.description}</p>
                <Link 
                  href="https://portal.delyorkcreative.academy/student/apply" 
                  className="inline-block bg-white text-black px-6 py-2 font-semibold hover:bg-gray-100 transition-colors"
                >
                  Enroll Now
                </Link>
              </div>
            </div>
          ))}
        </div>

      </div>
    </div>
  )
}
